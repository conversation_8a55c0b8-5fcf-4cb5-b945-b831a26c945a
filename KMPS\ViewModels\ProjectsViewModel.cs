using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using KMPS.Models;

namespace KMPS.ViewModels
{
    public class ProjectsViewModel : BaseViewModel
    {
        private ObservableCollection<Project> _projects;
        private Project _selectedProject;
        private string _searchText;
        private bool _isLoading;

        public ObservableCollection<Project> Projects
        {
            get => _projects;
            set => SetProperty(ref _projects, value);
        }

        public Project SelectedProject
        {
            get => _selectedProject;
            set => SetProperty(ref _selectedProject, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    // TODO: Implement search functionality
                    FilterProjects();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public ICommand AddProjectCommand { get; }
        public ICommand EditProjectCommand { get; }
        public ICommand DeleteProjectCommand { get; }
        public ICommand RefreshCommand { get; }

        private readonly KMPSDbContext _dbContext;

        public ProjectsViewModel(KMPSDbContext dbContext)
        {
            _dbContext = dbContext;
            
            AddProjectCommand = new RelayCommand(_ => AddProject());
            EditProjectCommand = new RelayCommand(_ => EditProject(), _ => SelectedProject != null);
            DeleteProjectCommand = new RelayCommand(_ => DeleteProject(), _ => SelectedProject != null);
            RefreshCommand = new RelayCommand(_ => RefreshProjectsAsync());

            InitializeAsync();
        }

        private async System.Threading.Tasks.Task InitializeAsync()
        {
            await LoadProjectsAsync();
        }

        private async System.Threading.Tasks.Task LoadProjectsAsync()
        {
            IsLoading = true;
            try
            {
                // TODO: Implement loading projects from database
                // Projects = new ObservableCollection<Project>(await _dbContext.Projects
                //     .Include(p => p.Tasks)
                //     .Include(p => p.Documents)
                //     .ToListAsync());
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void FilterProjects()
        {
            // TODO: Implement project filtering based on SearchText
        }

        private void AddProject()
        {
            // TODO: Implement add project functionality
        }

        private void EditProject()
        {
            // TODO: Implement edit project functionality
        }

        private void DeleteProject()
        {
            // TODO: Implement delete project functionality with confirmation
        }

        private void RefreshProjectsAsync()
        {
            // TODO: Implement refresh functionality
            LoadProjectsAsync();
        }
    }
}
