<Page x:Class="KMPS.Views.TasksView"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      Title="المهام">
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Text="المهام"
                   Grid.Row="0"
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0 0 0 16"/>

        <StackPanel Grid.Row="1" 
                    Orientation="Horizontal" 
                    Margin="0 0 0 16">
            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                    materialDesign:ButtonAssist.CornerRadius="4"
                    Command="{Binding AddTaskCommand}">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Plus" 
                                           Width="24" 
                                           Height="24"/>
                    <TextBlock Text="مهمة جديدة" 
                             Margin="8 0 0 0"/>
                </StackPanel>
            </Button>
        </StackPanel>

        <materialDesign:Card Grid.Row="2">
            <DataGrid x:Name="TasksDataGrid"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      ItemsSource="{Binding Tasks}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم المهمة" 
                                      Binding="{Binding TaskId}"/>
                    <DataGridTextColumn Header="المشروع" 
                                      Binding="{Binding Project.ProjectName}"/>
                    <DataGridTextColumn Header="اسم المهمة" 
                                      Binding="{Binding TaskName}"/>
                    <DataGridTextColumn Header="الموظف المسؤول" 
                                      Binding="{Binding AssignedTo.FullName}"/>
                    <DataGridTextColumn Header="تاريخ البدء" 
                                      Binding="{Binding StartDate, StringFormat=\{0:dd/MM/yyyy\}}"/>
                    <DataGridTextColumn Header="تاريخ الانتهاء" 
                                      Binding="{Binding EndDate, StringFormat=\{0:dd/MM/yyyy\}}"/>
                    <DataGridTextColumn Header="الحالة" 
                                      Binding="{Binding Status}"/>
                    <DataGridTextColumn Header="نسبة الإنجاز" 
                                      Binding="{Binding Progress, StringFormat=\{0\}%}"/>
                    <DataGridTemplateColumn Header="الإجراءات">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            Command="{Binding DataContext.EditTaskCommand, RelativeSource={RelativeSource AncestorType=UserControl}}">
                                        <materialDesign:PackIcon Kind="Pencil"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            Command="{Binding DataContext.DeleteTaskCommand, RelativeSource={RelativeSource AncestorType=UserControl}}">
                                        <materialDesign:PackIcon Kind="Delete"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
