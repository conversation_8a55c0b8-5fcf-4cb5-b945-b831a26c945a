
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace KMPS.Models
{
    public class Department
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم القسم مطلوب")]
        [StringLength(100, ErrorMessage = "اسم القسم يجب أن يكون أقل من 100 حرف")]
        public required string Name { get; set; }

        [StringLength(500, ErrorMessage = "وصف القسم يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [StringLength(100, ErrorMessage = "اسم المدير يجب أن يكون أقل من 100 حرف")]
        public string? Manager { get; set; }

        public string? Location { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public decimal? Budget { get; set; }
        public string Status { get; set; } = "نشط"; // Active

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? ModifiedAt { get; set; }

        // Navigation Properties
        public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();

        // Computed Properties
        public int EmployeeCount => Employees?.Count ?? 0;
        public string DisplayName => $"{Name} ({EmployeeCount} موظف)";

        // Methods
        public void UpdateModifiedDate()
        {
            ModifiedAt = DateTime.Now;
        }

        public bool HasEmployees()
        {
            return Employees != null && Employees.Count > 0;
        }

        public override string ToString()
        {
            return Name;
        }
    }
}
