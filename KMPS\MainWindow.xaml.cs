using System.Windows;
using KMPS.ViewModels;
using MahApps.Metro.Controls;

namespace KMPS
{
    public partial class MainWindow : MetroWindow
    {
        private readonly DashboardViewModel _dashboardViewModel;
        private readonly ProjectsViewModel _projectsViewModel;
        private readonly TasksViewModel _tasksViewModel;
        private readonly EmployeesViewModel _employeesViewModel;

        public MainWindow(
            DashboardViewModel dashboardViewModel,
            ProjectsViewModel projectsViewModel,
            TasksViewModel tasksViewModel,
            EmployeesViewModel employeesViewModel)
        {
            InitializeComponent();

            _dashboardViewModel = dashboardViewModel;
            _projectsViewModel = projectsViewModel;
            _tasksViewModel = tasksViewModel;
            _employeesViewModel = employeesViewModel;

            // Set initial view
            NavigateToDashboard();
        }

        private void InitializeComponent()
        {
            throw new NotImplementedException();
        }

        private void NavigateToDashboard()
        {
            MainContent.Content = new Views.DashboardView { DataContext = _dashboardViewModel };
        }

        private void NavigateToProjects()
        {
            MainContent.Content = new Views.ProjectsView { DataContext = _projectsViewModel };
        }

        private void NavigateToTasks()
        {
            MainContent.Content = new Views.TasksView { DataContext = _tasksViewModel };
        }

        private void NavigateToEmployees()
        {
            MainContent.Content = new Views.EmployeesView { DataContext = _employeesViewModel };
        }

        private void NavigateToReports()
        {
            MainContent.Content = new Views.ReportsView();
        }

        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToDashboard();
        }

        private void ProjectsButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToProjects();
        }

        private void TasksButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToTasks();
        }

        private void EmployeesButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToEmployees();
        }

        private void ReportsButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToReports();
        }
    }
}