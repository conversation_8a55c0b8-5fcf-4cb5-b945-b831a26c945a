using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using KMPS.Models;

namespace KMPS.ViewModels
{
    public class TasksViewModel : BaseViewModel
    {
        private ObservableCollection<Models.Task> _tasks;
        private Models.Task _selectedTask;
        private string _searchText;
        private bool _isLoading;
        private bool _showCompletedTasks;

        public ObservableCollection<Models.Task> Tasks
        {
            get => _tasks;
            set => SetProperty(ref _tasks, value);
        }

        public Models.Task SelectedTask
        {
            get => _selectedTask;
            set => SetProperty(ref _selectedTask, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    FilterTasks();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool ShowCompletedTasks
        {
            get => _showCompletedTasks;
            set
            {
                if (SetProperty(ref _showCompletedTasks, value))
                {
                    FilterTasks();
                }
            }
        }

        public ICommand AddTaskCommand { get; }
        public ICommand EditTaskCommand { get; }
        public ICommand DeleteTaskCommand { get; }
        public ICommand CompleteTaskCommand { get; }
        public ICommand RefreshCommand { get; }

        private readonly KMPSDbContext _dbContext;

        public TasksViewModel(KMPSDbContext dbContext)
        {
            _dbContext = dbContext;
            
            AddTaskCommand = new RelayCommand(_ => AddTask());
            EditTaskCommand = new RelayCommand(_ => EditTask(), _ => SelectedTask != null);
            DeleteTaskCommand = new RelayCommand(_ => DeleteTask(), _ => SelectedTask != null);
            CompleteTaskCommand = new RelayCommand(_ => CompleteTask(), _ => SelectedTask != null && SelectedTask.Status != "مكتملة");
            RefreshCommand = new RelayCommand(_ => RefreshTasksAsync());

            InitializeAsync();
        }

        private async System.Threading.Tasks.Task InitializeAsync()
        {
            await LoadTasksAsync();
        }

        private async System.Threading.Tasks.Task LoadTasksAsync()
        {
            IsLoading = true;
            try
            {
                // TODO: Implement loading tasks from database
                // Tasks = new ObservableCollection<Models.Task>(await _dbContext.Tasks
                //     .Include(t => t.Project)
                //     .Include(t => t.AssignedTo)
                //     .ToListAsync());
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void FilterTasks()
        {
            // TODO: Implement task filtering based on SearchText and ShowCompletedTasks
        }

        private void AddTask()
        {
            // TODO: Implement add task functionality
        }

        private void EditTask()
        {
            // TODO: Implement edit task functionality
        }

        private void DeleteTask()
        {
            // TODO: Implement delete task functionality with confirmation
        }

        private void CompleteTask()
        {
            // TODO: Implement complete task functionality
        }

        private void RefreshTasksAsync()
        {
            // TODO: Implement refresh functionality
            LoadTasksAsync();
        }
    }
}
